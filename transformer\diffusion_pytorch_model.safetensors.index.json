{"metadata": {"total_size": 15868645600}, "weight_map": {"context_refiner.0.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "context_refiner.0.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "context_refiner.0.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "context_refiner.0.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "context_refiner.0.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "context_refiner.0.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "context_refiner.0.feed_forward.linear_1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "context_refiner.0.feed_forward.linear_2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "context_refiner.0.feed_forward.linear_3.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "context_refiner.0.ffn_norm1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "context_refiner.0.ffn_norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "context_refiner.0.norm1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "context_refiner.0.norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "context_refiner.1.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "context_refiner.1.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "context_refiner.1.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "context_refiner.1.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "context_refiner.1.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "context_refiner.1.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "context_refiner.1.feed_forward.linear_1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "context_refiner.1.feed_forward.linear_2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "context_refiner.1.feed_forward.linear_3.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "context_refiner.1.ffn_norm1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "context_refiner.1.ffn_norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "context_refiner.1.norm1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "context_refiner.1.norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "image_index_embedding": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.0.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.0.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.0.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.0.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.0.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.0.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.0.feed_forward.linear_1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.0.feed_forward.linear_2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.0.feed_forward.linear_3.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.0.ffn_norm1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.0.ffn_norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.0.norm1.linear.bias": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.0.norm1.linear.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.0.norm1.norm.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.0.norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.1.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.1.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.1.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.1.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.1.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.1.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.1.feed_forward.linear_1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.1.feed_forward.linear_2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.1.feed_forward.linear_3.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.1.ffn_norm1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.1.ffn_norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.1.norm1.linear.bias": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.1.norm1.linear.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.1.norm1.norm.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.1.norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.10.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.10.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.10.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.10.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.10.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.10.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.10.feed_forward.linear_1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.10.feed_forward.linear_2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.10.feed_forward.linear_3.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.10.ffn_norm1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.10.ffn_norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.10.norm1.linear.bias": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.10.norm1.linear.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.10.norm1.norm.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.10.norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.11.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.11.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.11.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.11.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.11.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.11.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.11.feed_forward.linear_1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.11.feed_forward.linear_2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.11.feed_forward.linear_3.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.11.ffn_norm1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.11.ffn_norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.11.norm1.linear.bias": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.11.norm1.linear.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.11.norm1.norm.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.11.norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.12.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.12.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.12.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.12.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.12.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.12.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.12.feed_forward.linear_1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.12.feed_forward.linear_2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.12.feed_forward.linear_3.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.12.ffn_norm1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.12.ffn_norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.12.norm1.linear.bias": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.12.norm1.linear.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.12.norm1.norm.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.12.norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.13.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.13.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.13.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.13.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.13.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.13.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.13.feed_forward.linear_1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.13.feed_forward.linear_2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.13.feed_forward.linear_3.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.13.ffn_norm1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.13.ffn_norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.13.norm1.linear.bias": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.13.norm1.linear.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.13.norm1.norm.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.13.norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.14.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.14.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.14.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.14.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.14.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.14.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.14.feed_forward.linear_1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.14.feed_forward.linear_2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.14.feed_forward.linear_3.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.14.ffn_norm1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.14.ffn_norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.14.norm1.linear.bias": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.14.norm1.linear.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.14.norm1.norm.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.14.norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.15.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.15.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.15.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.15.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.15.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.15.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.15.feed_forward.linear_1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.15.feed_forward.linear_2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.15.feed_forward.linear_3.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.15.ffn_norm1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.15.ffn_norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.15.norm1.linear.bias": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.15.norm1.linear.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.15.norm1.norm.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.15.norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.16.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.16.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.16.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.16.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.16.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.16.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.16.feed_forward.linear_1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.16.feed_forward.linear_2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.16.feed_forward.linear_3.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.16.ffn_norm1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.16.ffn_norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.16.norm1.linear.bias": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.16.norm1.linear.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.16.norm1.norm.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.16.norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.17.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.17.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.17.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.17.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.17.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.17.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.17.feed_forward.linear_1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.17.feed_forward.linear_2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.17.feed_forward.linear_3.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.17.ffn_norm1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.17.ffn_norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.17.norm1.linear.bias": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.17.norm1.linear.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.17.norm1.norm.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.17.norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.18.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.18.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.18.attn.to_k.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.18.attn.to_out.0.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.18.attn.to_q.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.18.attn.to_v.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.18.feed_forward.linear_1.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.18.feed_forward.linear_2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.18.feed_forward.linear_3.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.18.ffn_norm1.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.18.ffn_norm2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.18.norm1.linear.bias": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.18.norm1.linear.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.18.norm1.norm.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.18.norm2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.19.attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.19.attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.19.attn.to_k.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.19.attn.to_out.0.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.19.attn.to_q.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.19.attn.to_v.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.19.feed_forward.linear_1.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.19.feed_forward.linear_2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.19.feed_forward.linear_3.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.19.ffn_norm1.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.19.ffn_norm2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.19.norm1.linear.bias": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.19.norm1.linear.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.19.norm1.norm.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.19.norm2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.2.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.2.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.2.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.2.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.2.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.2.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.2.feed_forward.linear_1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.2.feed_forward.linear_2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.2.feed_forward.linear_3.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.2.ffn_norm1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.2.ffn_norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.2.norm1.linear.bias": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.2.norm1.linear.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.2.norm1.norm.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.2.norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.20.attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.20.attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.20.attn.to_k.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.20.attn.to_out.0.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.20.attn.to_q.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.20.attn.to_v.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.20.feed_forward.linear_1.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.20.feed_forward.linear_2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.20.feed_forward.linear_3.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.20.ffn_norm1.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.20.ffn_norm2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.20.norm1.linear.bias": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.20.norm1.linear.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.20.norm1.norm.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.20.norm2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.21.attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.21.attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.21.attn.to_k.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.21.attn.to_out.0.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.21.attn.to_q.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.21.attn.to_v.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.21.feed_forward.linear_1.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.21.feed_forward.linear_2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.21.feed_forward.linear_3.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.21.ffn_norm1.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.21.ffn_norm2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.21.norm1.linear.bias": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.21.norm1.linear.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.21.norm1.norm.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.21.norm2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.22.attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.22.attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.22.attn.to_k.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.22.attn.to_out.0.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.22.attn.to_q.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.22.attn.to_v.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.22.feed_forward.linear_1.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.22.feed_forward.linear_2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.22.feed_forward.linear_3.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.22.ffn_norm1.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.22.ffn_norm2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.22.norm1.linear.bias": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.22.norm1.linear.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.22.norm1.norm.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.22.norm2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.23.attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.23.attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.23.attn.to_k.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.23.attn.to_out.0.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.23.attn.to_q.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.23.attn.to_v.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.23.feed_forward.linear_1.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.23.feed_forward.linear_2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.23.feed_forward.linear_3.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.23.ffn_norm1.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.23.ffn_norm2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.23.norm1.linear.bias": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.23.norm1.linear.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.23.norm1.norm.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.23.norm2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.24.attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.24.attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.24.attn.to_k.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.24.attn.to_out.0.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.24.attn.to_q.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.24.attn.to_v.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.24.feed_forward.linear_1.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.24.feed_forward.linear_2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.24.feed_forward.linear_3.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.24.ffn_norm1.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.24.ffn_norm2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.24.norm1.linear.bias": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.24.norm1.linear.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.24.norm1.norm.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.24.norm2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.25.attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.25.attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.25.attn.to_k.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.25.attn.to_out.0.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.25.attn.to_q.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.25.attn.to_v.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.25.feed_forward.linear_1.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.25.feed_forward.linear_2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.25.feed_forward.linear_3.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.25.ffn_norm1.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.25.ffn_norm2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.25.norm1.linear.bias": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.25.norm1.linear.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.25.norm1.norm.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.25.norm2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.26.attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.26.attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.26.attn.to_k.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.26.attn.to_out.0.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.26.attn.to_q.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.26.attn.to_v.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.26.feed_forward.linear_1.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.26.feed_forward.linear_2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.26.feed_forward.linear_3.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.26.ffn_norm1.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.26.ffn_norm2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.26.norm1.linear.bias": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.26.norm1.linear.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.26.norm1.norm.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.26.norm2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.27.attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.27.attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.27.attn.to_k.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.27.attn.to_out.0.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.27.attn.to_q.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.27.attn.to_v.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.27.feed_forward.linear_1.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.27.feed_forward.linear_2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.27.feed_forward.linear_3.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.27.ffn_norm1.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.27.ffn_norm2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.27.norm1.linear.bias": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.27.norm1.linear.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.27.norm1.norm.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.27.norm2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.28.attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.28.attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.28.attn.to_k.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.28.attn.to_out.0.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.28.attn.to_q.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.28.attn.to_v.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.28.feed_forward.linear_1.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.28.feed_forward.linear_2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.28.feed_forward.linear_3.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.28.ffn_norm1.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.28.ffn_norm2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.28.norm1.linear.bias": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.28.norm1.linear.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.28.norm1.norm.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.28.norm2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.29.attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.29.attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.29.attn.to_k.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.29.attn.to_out.0.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.29.attn.to_q.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.29.attn.to_v.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.29.feed_forward.linear_1.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.29.feed_forward.linear_2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.29.feed_forward.linear_3.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.29.ffn_norm1.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.29.ffn_norm2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.29.norm1.linear.bias": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.29.norm1.linear.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.29.norm1.norm.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.29.norm2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.3.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.3.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.3.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.3.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.3.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.3.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.3.feed_forward.linear_1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.3.feed_forward.linear_2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.3.feed_forward.linear_3.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.3.ffn_norm1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.3.ffn_norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.3.norm1.linear.bias": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.3.norm1.linear.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.3.norm1.norm.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.3.norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.30.attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.30.attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.30.attn.to_k.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.30.attn.to_out.0.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.30.attn.to_q.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.30.attn.to_v.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.30.feed_forward.linear_1.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.30.feed_forward.linear_2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.30.feed_forward.linear_3.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.30.ffn_norm1.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.30.ffn_norm2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.30.norm1.linear.bias": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.30.norm1.linear.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.30.norm1.norm.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.30.norm2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.31.attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.31.attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.31.attn.to_k.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.31.attn.to_out.0.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.31.attn.to_q.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.31.attn.to_v.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.31.feed_forward.linear_1.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.31.feed_forward.linear_2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.31.feed_forward.linear_3.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.31.ffn_norm1.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.31.ffn_norm2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.31.norm1.linear.bias": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.31.norm1.linear.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.31.norm1.norm.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.31.norm2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "layers.4.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.4.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.4.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.4.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.4.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.4.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.4.feed_forward.linear_1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.4.feed_forward.linear_2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.4.feed_forward.linear_3.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.4.ffn_norm1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.4.ffn_norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.4.norm1.linear.bias": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.4.norm1.linear.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.4.norm1.norm.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.4.norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.5.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.5.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.5.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.5.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.5.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.5.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.5.feed_forward.linear_1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.5.feed_forward.linear_2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.5.feed_forward.linear_3.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.5.ffn_norm1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.5.ffn_norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.5.norm1.linear.bias": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.5.norm1.linear.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.5.norm1.norm.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.5.norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.6.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.6.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.6.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.6.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.6.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.6.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.6.feed_forward.linear_1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.6.feed_forward.linear_2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.6.feed_forward.linear_3.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.6.ffn_norm1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.6.ffn_norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.6.norm1.linear.bias": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.6.norm1.linear.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.6.norm1.norm.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.6.norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.7.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.7.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.7.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.7.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.7.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.7.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.7.feed_forward.linear_1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.7.feed_forward.linear_2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.7.feed_forward.linear_3.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.7.ffn_norm1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.7.ffn_norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.7.norm1.linear.bias": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.7.norm1.linear.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.7.norm1.norm.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.7.norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.8.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.8.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.8.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.8.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.8.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.8.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.8.feed_forward.linear_1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.8.feed_forward.linear_2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.8.feed_forward.linear_3.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.8.ffn_norm1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.8.ffn_norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.8.norm1.linear.bias": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.8.norm1.linear.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.8.norm1.norm.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.8.norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.9.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.9.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.9.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.9.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.9.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.9.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.9.feed_forward.linear_1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.9.feed_forward.linear_2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.9.feed_forward.linear_3.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.9.ffn_norm1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.9.ffn_norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.9.norm1.linear.bias": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.9.norm1.linear.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.9.norm1.norm.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "layers.9.norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "noise_refiner.0.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "noise_refiner.0.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "noise_refiner.0.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "noise_refiner.0.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "noise_refiner.0.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "noise_refiner.0.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "noise_refiner.0.feed_forward.linear_1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "noise_refiner.0.feed_forward.linear_2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "noise_refiner.0.feed_forward.linear_3.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "noise_refiner.0.ffn_norm1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "noise_refiner.0.ffn_norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "noise_refiner.0.norm1.linear.bias": "diffusion_pytorch_model-00001-of-00002.safetensors", "noise_refiner.0.norm1.linear.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "noise_refiner.0.norm1.norm.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "noise_refiner.0.norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "noise_refiner.1.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "noise_refiner.1.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "noise_refiner.1.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "noise_refiner.1.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "noise_refiner.1.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "noise_refiner.1.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "noise_refiner.1.feed_forward.linear_1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "noise_refiner.1.feed_forward.linear_2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "noise_refiner.1.feed_forward.linear_3.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "noise_refiner.1.ffn_norm1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "noise_refiner.1.ffn_norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "noise_refiner.1.norm1.linear.bias": "diffusion_pytorch_model-00001-of-00002.safetensors", "noise_refiner.1.norm1.linear.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "noise_refiner.1.norm1.norm.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "noise_refiner.1.norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "norm_out.linear_1.bias": "diffusion_pytorch_model-00002-of-00002.safetensors", "norm_out.linear_1.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "norm_out.linear_2.bias": "diffusion_pytorch_model-00002-of-00002.safetensors", "norm_out.linear_2.weight": "diffusion_pytorch_model-00002-of-00002.safetensors", "ref_image_patch_embedder.bias": "diffusion_pytorch_model-00001-of-00002.safetensors", "ref_image_patch_embedder.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "ref_image_refiner.0.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "ref_image_refiner.0.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "ref_image_refiner.0.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "ref_image_refiner.0.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "ref_image_refiner.0.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "ref_image_refiner.0.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "ref_image_refiner.0.feed_forward.linear_1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "ref_image_refiner.0.feed_forward.linear_2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "ref_image_refiner.0.feed_forward.linear_3.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "ref_image_refiner.0.ffn_norm1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "ref_image_refiner.0.ffn_norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "ref_image_refiner.0.norm1.linear.bias": "diffusion_pytorch_model-00001-of-00002.safetensors", "ref_image_refiner.0.norm1.linear.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "ref_image_refiner.0.norm1.norm.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "ref_image_refiner.0.norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "ref_image_refiner.1.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "ref_image_refiner.1.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "ref_image_refiner.1.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "ref_image_refiner.1.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "ref_image_refiner.1.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "ref_image_refiner.1.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "ref_image_refiner.1.feed_forward.linear_1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "ref_image_refiner.1.feed_forward.linear_2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "ref_image_refiner.1.feed_forward.linear_3.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "ref_image_refiner.1.ffn_norm1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "ref_image_refiner.1.ffn_norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "ref_image_refiner.1.norm1.linear.bias": "diffusion_pytorch_model-00001-of-00002.safetensors", "ref_image_refiner.1.norm1.linear.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "ref_image_refiner.1.norm1.norm.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "ref_image_refiner.1.norm2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "time_caption_embed.caption_embedder.0.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "time_caption_embed.caption_embedder.1.bias": "diffusion_pytorch_model-00001-of-00002.safetensors", "time_caption_embed.caption_embedder.1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "time_caption_embed.timestep_embedder.linear_1.bias": "diffusion_pytorch_model-00001-of-00002.safetensors", "time_caption_embed.timestep_embedder.linear_1.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "time_caption_embed.timestep_embedder.linear_2.bias": "diffusion_pytorch_model-00001-of-00002.safetensors", "time_caption_embed.timestep_embedder.linear_2.weight": "diffusion_pytorch_model-00001-of-00002.safetensors", "x_embedder.bias": "diffusion_pytorch_model-00001-of-00002.safetensors", "x_embedder.weight": "diffusion_pytorch_model-00001-of-00002.safetensors"}}