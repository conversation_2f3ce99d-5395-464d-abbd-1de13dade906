# OmniGen2 RTX 5080 + CUDA 12.8 环境设置脚本
# PowerShell 版本

Write-Host "=== OmniGen2 RTX 5080 + CUDA 12.8 环境设置 ===" -ForegroundColor Green
Write-Host "目标配置:" -ForegroundColor Yellow
Write-Host "- RTX 5080 16GB 显卡"
Write-Host "- CUDA 12.8"
Write-Host "- Python 3.11"
Write-Host "- PyTorch 2.6.0+cu128"
Write-Host ""

# 激活 conda 环境
Write-Host "激活 models 环境..." -ForegroundColor Yellow
conda activate models

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 无法激活 models 环境" -ForegroundColor Red
    Write-Host "请确保已创建 models 环境: conda create -n models python=3.11 -y"
    exit 1
}

Write-Host "✅ models 环境已激活" -ForegroundColor Green

# 运行 Python 安装脚本
Write-Host "运行 Python 安装脚本..." -ForegroundColor Yellow
python setup_models_env.py

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ 环境设置完成！" -ForegroundColor Green
    Write-Host ""
    Write-Host "现在可以运行 OmniGen2 了:" -ForegroundColor Yellow
    Write-Host "conda activate models"
    Write-Host "cd OmniGen2"
    Write-Host "python inference.py --model_path pretrained_models/OmniGen2 --instruction 'A cute cat' --output_image_path outputs/test.png"
} else {
    Write-Host "❌ 环境设置失败" -ForegroundColor Red
    Write-Host "请检查错误信息并手动解决"
}

Write-Host ""
Write-Host "按任意键继续..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
