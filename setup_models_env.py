#!/usr/bin/env python3
"""
OmniGen2 RTX 5080 + CUDA 12.8 环境设置脚本
专门针对 RTX 5080 16GB 显卡和 CUDA 12.8 优化
"""

import subprocess
import sys
import os

def run_command(command, description=""):
    """运行命令并处理错误"""
    print(f"\n{'='*60}")
    print(f"执行: {description}")
    print(f"命令: {command}")
    print('='*60)
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        print("✓ 成功")
        if result.stdout:
            print(f"输出: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 失败: {e}")
        if e.stdout:
            print(f"标准输出: {e.stdout}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False

def main():
    print("=== OmniGen2 RTX 5080 + CUDA 12.8 环境设置 ===")
    print("目标配置:")
    print("- RTX 5080 16GB 显卡")
    print("- CUDA 12.8")
    print("- Python 3.11")
    print("- PyTorch 2.6.0+cu128")
    print()
    
    # 检查是否在正确的 conda 环境中
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'base')
    print(f"当前 conda 环境: {conda_env}")
    
    if conda_env != 'models':
        print("⚠ 警告: 请先激活 models 环境")
        print("运行: conda activate models")
        return False
    
    # 安装 PyTorch 2.6.0 + CUDA 12.8
    pytorch_cmd = "pip install torch==2.6.0 torchvision==0.21.0 torchaudio==2.6.0 --index-url https://download.pytorch.org/whl/cu128"
    if not run_command(pytorch_cmd, "安装 PyTorch 2.6.0 + CUDA 12.8"):
        print("PyTorch 安装失败，尝试备用源...")
        pytorch_cmd_alt = "pip install torch==2.6.0 torchvision==0.21.0 torchaudio==2.6.0 --extra-index-url https://download.pytorch.org/whl/cu128"
        if not run_command(pytorch_cmd_alt, "使用备用源安装 PyTorch"):
            return False
    
    # 验证 PyTorch CUDA 支持
    verify_cmd = 'python -c "import torch; print(f\'PyTorch: {torch.__version__}\'); print(f\'CUDA available: {torch.cuda.is_available()}\'); print(f\'CUDA version: {torch.version.cuda if torch.cuda.is_available() else \'N/A\'}\'); print(f\'GPU count: {torch.cuda.device_count()}\'); print(f\'GPU name: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else \'N/A\'}\');"'
    run_command(verify_cmd, "验证 PyTorch CUDA 支持")
    
    # 安装核心依赖
    core_packages = [
        "timm>=0.9.0",
        "einops>=0.6.0", 
        "accelerate>=0.20.0",
        "transformers>=4.51.3",
        "diffusers>=0.21.0",
        "numpy<2.0",  # 确保兼容性
    ]
    
    for package in core_packages:
        run_command(f"pip install {package}", f"安装 {package}")
    
    # 安装图像处理和工具包
    utility_packages = [
        "opencv-python-headless>=4.8.0",
        "scipy>=1.10.0",
        "Pillow>=9.5.0",
        "matplotlib>=3.7.0",
        "tqdm>=4.65.0",
        "omegaconf>=2.3.0",
        "python-dotenv>=1.0.0",
        "ninja>=1.11.0",
        "ipykernel>=6.25.0",
        "wheel>=0.40.0"
    ]
    
    for package in utility_packages:
        run_command(f"pip install {package}", f"安装 {package}")
    
    # 安装可选包
    optional_packages = [
        "wandb>=0.15.0",  # 训练日志
        "gradio",  # Web UI
    ]
    
    print("\n安装可选包...")
    for package in optional_packages:
        run_command(f"pip install {package}", f"安装 {package} (可选)")
    
    # 尝试安装 Flash Attention (RTX 5080 应该支持)
    print("\n尝试安装 Flash Attention (RTX 5080 优化)...")
    flash_attn_cmd = "pip install flash-attn>=2.7.0 --no-build-isolation"
    if not run_command(flash_attn_cmd, "安装 Flash Attention"):
        print("⚠ Flash Attention 安装失败，但不影响基本功能")
        print("可以稍后手动安装或使用预编译版本")
    
    # 最终验证
    print("\n" + "="*60)
    print("最终环境验证")
    print("="*60)
    
    final_verify = '''python -c "
import torch
import torchvision
import transformers
import diffusers
import accelerate
print('✓ 所有核心包导入成功')
print(f'PyTorch: {torch.__version__}')
print(f'TorchVision: {torchvision.__version__}')
print(f'Transformers: {transformers.__version__}')
print(f'Diffusers: {diffusers.__version__}')
print(f'CUDA available: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'GPU: {torch.cuda.get_device_name(0)}')
    print(f'VRAM: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB')
"'''
    
    run_command(final_verify, "最终环境验证")
    
    print("\n" + "="*60)
    print("环境设置完成！")
    print("="*60)
    print("下一步:")
    print("1. 确保激活 models 环境: conda activate models")
    print("2. 运行测试: python inference.py --model_path pretrained_models/OmniGen2 ...")
    print("3. 如果遇到问题，检查 CUDA 驱动是否支持 CUDA 12.8")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
