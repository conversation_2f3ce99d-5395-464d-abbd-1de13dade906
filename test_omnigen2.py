#!/usr/bin/env python3
"""
OmniGen2 环境测试脚本
验证所有依赖是否正确安装并测试基本功能
"""

import sys
import os
import traceback

# 添加 OmniGen2 目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
omnigen2_dir = os.path.join(current_dir, "OmniGen2")
if os.path.exists(omnigen2_dir):
    sys.path.insert(0, omnigen2_dir)
    print(f"添加路径: {omnigen2_dir}")
else:
    # 如果已经在 OmniGen2 目录中
    sys.path.insert(0, current_dir)
    print(f"添加路径: {current_dir}")

def test_imports():
    """测试所有必要的包导入"""
    print("=== 测试包导入 ===")
    
    packages = [
        ("torch", "PyTorch"),
        ("torchvision", "TorchVision"),
        ("transformers", "Transformers"),
        ("diffusers", "Diffusers"),
        ("accelerate", "Accelerate"),
        ("timm", "TIMM"),
        ("einops", "Einops"),
        ("numpy", "NumPy"),
        ("cv2", "OpenCV"),
        ("scipy", "SciPy"),
        ("matplotlib", "Matplotlib"),
        ("omegaconf", "OmegaConf"),
        ("gradio", "Gradio"),
    ]
    
    failed_imports = []
    
    for package, name in packages:
        try:
            __import__(package)
            print(f"✅ {name}")
        except ImportError as e:
            print(f"❌ {name}: {e}")
            failed_imports.append(name)
    
    return failed_imports

def test_cuda():
    """测试 CUDA 功能"""
    print("\n=== 测试 CUDA ===")
    
    try:
        import torch
        print(f"PyTorch 版本: {torch.__version__}")
        print(f"CUDA 可用: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"CUDA 版本: {torch.version.cuda}")
            print(f"GPU 数量: {torch.cuda.device_count()}")
            print(f"GPU 名称: {torch.cuda.get_device_name(0)}")
            print(f"显存大小: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
            
            # 测试简单的 CUDA 操作
            x = torch.randn(100, 100).cuda()
            y = torch.randn(100, 100).cuda()
            z = torch.mm(x, y)
            print("✅ CUDA 矩阵运算测试成功")
            return True
        else:
            print("❌ CUDA 不可用")
            return False
            
    except Exception as e:
        print(f"❌ CUDA 测试失败: {e}")
        return False

def test_omnigen2_imports():
    """测试 OmniGen2 相关导入"""
    print("\n=== 测试 OmniGen2 导入 ===")
    
    try:
        # 测试 OmniGen2 核心组件
        from omnigen2.pipelines.omnigen2.pipeline_omnigen2 import OmniGen2Pipeline
        print("✅ OmniGen2Pipeline 导入成功")
        
        from omnigen2.models.transformers.transformer_omnigen2 import OmniGen2Transformer2DModel
        print("✅ OmniGen2Transformer2DModel 导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ OmniGen2 导入失败: {e}")
        print("请确保在 OmniGen2 目录中运行此脚本")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_model_loading():
    """测试模型加载"""
    print("\n=== 测试模型加载 ===")
    
    try:
        from omnigen2.pipelines.omnigen2.pipeline_omnigen2 import OmniGen2Pipeline
        
        model_path = "pretrained_models/OmniGen2"
        print(f"尝试加载模型: {model_path}")
        
        # 这里只测试是否能创建 pipeline 对象，不实际加载权重
        print("✅ 模型路径检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 模型加载测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=== OmniGen2 RTX 5080 环境测试 ===")
    print("测试环境: models conda 环境")
    print("目标硬件: RTX 5080 16GB + CUDA 12.8")
    print()
    
    # 测试包导入
    failed_imports = test_imports()
    
    # 测试 CUDA
    cuda_ok = test_cuda()
    
    # 测试 OmniGen2 导入
    omnigen2_ok = test_omnigen2_imports()
    
    # 测试模型加载
    model_ok = test_model_loading()
    
    # 总结
    print("\n" + "="*50)
    print("测试总结:")
    print("="*50)
    
    if failed_imports:
        print(f"❌ 包导入失败: {', '.join(failed_imports)}")
    else:
        print("✅ 所有包导入成功")
    
    if cuda_ok:
        print("✅ CUDA 功能正常")
    else:
        print("❌ CUDA 功能异常")
    
    if omnigen2_ok:
        print("✅ OmniGen2 导入成功")
    else:
        print("❌ OmniGen2 导入失败")
    
    if model_ok:
        print("✅ 模型加载测试通过")
    else:
        print("❌ 模型加载测试失败")
    
    # 整体评估
    if not failed_imports and cuda_ok and omnigen2_ok and model_ok:
        print("\n🎉 环境配置完美！可以开始使用 OmniGen2")
        print("建议下一步:")
        print("1. 运行文本到图像生成测试")
        print("2. 测试图像编辑功能")
        print("3. 开始游戏风格微调")
    else:
        print("\n⚠ 环境配置需要修复")
        print("请根据上述错误信息进行修复")
    
    return len(failed_imports) == 0 and cuda_ok and omnigen2_ok and model_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
