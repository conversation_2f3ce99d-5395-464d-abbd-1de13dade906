@echo off
echo === OmniGen2 RTX 5080 + CUDA 12.8 环境设置 ===
echo 目标配置:
echo - RTX 5080 16GB 显卡
echo - CUDA 12.8
echo - Python 3.11
echo - PyTorch 2.6.0+cu128
echo.

echo 激活 models 环境...
call conda activate models
if %errorlevel% neq 0 (
    echo ❌ 无法激活 models 环境
    echo 请确保已创建 models 环境: conda create -n models python=3.11 -y
    pause
    exit /b 1
)

echo ✅ models 环境已激活
echo.

echo 验证 Python 环境...
python -c "import sys; print(f'Python: {sys.version}'); print(f'Environment: {sys.prefix}')"
echo.

echo 安装 PyTorch 2.6.0 + CUDA 12.8...
pip install torch==2.6.0 torchvision==0.21.0 torchaudio==2.6.0 --index-url https://download.pytorch.org/whl/cu128
if %errorlevel% neq 0 (
    echo 尝试备用源...
    pip install torch==2.6.0 torchvision==0.21.0 torchaudio==2.6.0 --extra-index-url https://download.pytorch.org/whl/cu128
)

echo.
echo 验证 PyTorch CUDA 支持...
python -c "import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA available: {torch.cuda.is_available()}'); print(f'CUDA version: {torch.version.cuda if torch.cuda.is_available() else \"N/A\"}'); print(f'GPU count: {torch.cuda.device_count()}'); print(f'GPU name: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else \"N/A\"}')"

echo.
echo 安装核心依赖...
pip install "numpy<2.0" timm>=0.9.0 einops>=0.6.0 accelerate>=0.20.0 transformers>=4.51.3 diffusers>=0.21.0

echo.
echo 安装图像处理和工具包...
pip install opencv-python-headless>=4.8.0 scipy>=1.10.0 "Pillow>=9.5.0" matplotlib>=3.7.0 tqdm>=4.65.0 omegaconf>=2.3.0 python-dotenv>=1.0.0 ninja>=1.11.0 ipykernel>=6.25.0 wheel>=0.40.0

echo.
echo 安装可选包...
pip install wandb>=0.15.0 gradio

echo.
echo 尝试安装 Flash Attention (RTX 5080 优化)...
pip install flash-attn>=2.7.0 --no-build-isolation
if %errorlevel% neq 0 (
    echo ⚠ Flash Attention 安装失败，但不影响基本功能
)

echo.
echo === 最终环境验证 ===
python -c "import torch, torchvision, transformers, diffusers, accelerate; print('✅ 所有核心包导入成功'); print(f'PyTorch: {torch.__version__}'); print(f'TorchVision: {torchvision.__version__}'); print(f'Transformers: {transformers.__version__}'); print(f'Diffusers: {diffusers.__version__}'); print(f'CUDA available: {torch.cuda.is_available()}'); print(f'GPU: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else \"N/A\"}'); print(f'VRAM: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB' if torch.cuda.is_available() else 'N/A')"

echo.
echo === 环境设置完成！ ===
echo 下一步:
echo 1. 确保激活 models 环境: conda activate models
echo 2. 进入项目目录: cd OmniGen2
echo 3. 运行测试: python inference.py --model_path pretrained_models/OmniGen2 --instruction "A cute cat" --output_image_path outputs/test.png
echo.
pause
