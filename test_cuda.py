import torch

print("=== PyTorch CUDA 测试 ===")
print(f"PyTorch 版本: {torch.__version__}")
print(f"CUDA 可用: {torch.cuda.is_available()}")

if torch.cuda.is_available():
    print(f"CUDA 版本: {torch.version.cuda}")
    print(f"GPU 数量: {torch.cuda.device_count()}")
    print(f"GPU 名称: {torch.cuda.get_device_name(0)}")
    print(f"显存大小: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    
    # 测试简单的 CUDA 操作
    x = torch.randn(1000, 1000).cuda()
    y = torch.randn(1000, 1000).cuda()
    z = torch.mm(x, y)
    print("✅ CUDA 矩阵运算测试成功")
else:
    print("❌ CUDA 不可用")
    print("请检查:")
    print("1. NVIDIA 驱动是否安装")
    print("2. CUDA 12.8 是否正确安装")
    print("3. 环境变量是否正确设置")
