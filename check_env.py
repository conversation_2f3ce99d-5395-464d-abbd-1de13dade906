import sys
import os

print("=== 环境检查 ===")
print(f"Python 版本: {sys.version}")
print(f"Python 路径: {sys.executable}")
print(f"环境路径: {sys.prefix}")
print(f"当前工作目录: {os.getcwd()}")

# 检查 conda 环境
conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
print(f"Conda 环境: {conda_env}")

# 检查是否在 models 环境中
if 'models' in sys.prefix or conda_env == 'models':
    print("✅ 在 models 环境中")
else:
    print("❌ 不在 models 环境中")
    print("请运行: conda activate models")
